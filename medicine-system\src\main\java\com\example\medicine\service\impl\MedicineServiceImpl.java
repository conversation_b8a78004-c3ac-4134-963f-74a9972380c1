package com.example.medicine.service.impl;

import com.example.medicine.dto.MedicineSearchParams;
import com.example.medicine.entity.Medicine;
import com.example.medicine.repository.MedicineRepository;
import com.example.medicine.repository.InventoryRepository;
import com.example.medicine.repository.PurchaseRepository;
import com.example.medicine.repository.SaleRepository;
import com.example.medicine.service.MedicineService;
import com.example.medicine.specification.MedicineSpecification;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class MedicineServiceImpl implements MedicineService {

    @Autowired
    private MedicineRepository medicineRepository;

    @Autowired
    private InventoryRepository inventoryRepository;

    @Autowired
    private PurchaseRepository purchaseRepository;

    @Autowired
    private SaleRepository saleRepository;

    @Override
    public List<Medicine> findAll() {
        return medicineRepository.findAll();
    }

    @Override
    public Medicine findById(Long id) {
        return medicineRepository.findById(id).orElse(null);
    }

    @Override
    public Medicine save(Medicine medicine) {
        return medicineRepository.save(medicine);
    }

    @Override
    @Transactional
    public void deleteById(Long id) {
        // 检查药品是否存在
        Medicine medicine = medicineRepository.findById(id).orElse(null);
        if (medicine == null) {
            throw new RuntimeException("药品不存在");
        }

        // 检查是否有库存记录
        long inventoryCount = inventoryRepository.findAll().stream()
            .filter(inventory -> inventory.getMedicineId().equals(id))
            .count();
        if (inventoryCount > 0) {
            throw new RuntimeException("无法删除药品：该药品存在库存记录，请先清理库存记录后再删除");
        }

        // 检查是否有采购记录
        long purchaseCount = purchaseRepository.findAll().stream()
            .filter(purchase -> purchase.getMedicineId().equals(id))
            .count();
        if (purchaseCount > 0) {
            throw new RuntimeException("无法删除药品：该药品存在采购记录，请先清理采购记录后再删除");
        }

        // 检查是否有销售记录
        long saleCount = saleRepository.findAll().stream()
            .filter(sale -> sale.getMedicineId().equals(id))
            .count();
        if (saleCount > 0) {
            throw new RuntimeException("无法删除药品：该药品存在销售记录，请先清理销售记录后再删除");
        }

        // 如果没有任何关联记录，则可以安全删除
        medicineRepository.deleteById(id);
    }

    @Override
    public Page<Medicine> searchMedicines(MedicineSearchParams params) {
        // 创建分页对象，按ID降序排列
        Pageable pageable = PageRequest.of(
            params.getPage() - 1, // Spring Data JPA 的页码从0开始
            params.getSize(),
            Sort.by(Sort.Direction.DESC, "id")
        );

        // 构建查询条件
        Specification<Medicine> specification = MedicineSpecification.buildSpecification(params);

        // 执行分页查询
        return medicineRepository.findAll(specification, pageable);
    }
}