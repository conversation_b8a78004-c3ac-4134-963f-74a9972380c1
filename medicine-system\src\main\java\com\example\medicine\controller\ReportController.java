package com.example.medicine.controller;

import com.example.medicine.common.Result;
import com.example.medicine.service.ReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/report")
@CrossOrigin
public class ReportController {

    @Autowired
    private ReportService reportService;

    /**
     * 获取报表概览数据
     */
    @GetMapping("/overview")
    public Result<Map<String, Object>> getOverviewData(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            Map<String, Object> data = reportService.getOverviewData(startDate, endDate);
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取概览数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取销售趋势数据
     */
    @GetMapping("/sales-trend")
    public Result<Map<String, Object>> getSalesTrendData(
            @RequestParam(defaultValue = "daily") String type,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        try {
            Map<String, Object> data = reportService.getSalesTrendData(type, startDate, endDate);
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取销售趋势数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取药品销售排行
     */
    @GetMapping("/medicine-rank")
    public Result<List<Map<String, Object>>> getMedicineRankData(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "5") Integer limit) {
        try {
            List<Map<String, Object>> data = reportService.getMedicineRankData(startDate, endDate, limit);
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取药品排行数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户分布数据
     */
    @GetMapping("/customer-distribution")
    public Result<List<Map<String, Object>>> getCustomerDistributionData() {
        try {
            List<Map<String, Object>> data = reportService.getCustomerDistributionData();
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取客户分布数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取供应商占比数据
     */
    @GetMapping("/supplier-ratio")
    public Result<List<Map<String, Object>>> getSupplierRatioData() {
        try {
            List<Map<String, Object>> data = reportService.getSupplierRatioData();
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取供应商占比数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取库存状态数据
     */
    @GetMapping("/inventory-status")
    public Result<List<Map<String, Object>>> getInventoryStatusData() {
        try {
            List<Map<String, Object>> data = reportService.getInventoryStatusData();
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取库存状态数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取热销药品排行
     */
    @GetMapping("/hot-medicines")
    public Result<List<Map<String, Object>>> getHotMedicinesData(
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "5") Integer limit) {
        try {
            List<Map<String, Object>> data = reportService.getHotMedicinesData(startDate, endDate, limit);
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取热销药品数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取库存预警数据
     */
    @GetMapping("/low-stock")
    public Result<List<Map<String, Object>>> getLowStockData() {
        try {
            List<Map<String, Object>> data = reportService.getLowStockData();
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取库存预警数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取仪表盘统计数据
     */
    @GetMapping("/dashboard-stats")
    public Result<Map<String, Object>> getDashboardStats() {
        try {
            Map<String, Object> data = reportService.getDashboardStats();
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取仪表盘统计数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取最近活动数据
     */
    @GetMapping("/recent-activities")
    public Result<List<Map<String, Object>>> getRecentActivities() {
        try {
            List<Map<String, Object>> data = reportService.getRecentActivities();
            return Result.success(data);
        } catch (Exception e) {
            return Result.error("获取最近活动数据失败: " + e.getMessage());
        }
    }
}