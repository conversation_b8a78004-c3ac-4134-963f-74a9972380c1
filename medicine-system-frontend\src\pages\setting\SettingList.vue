<template>
  <div class="setting-list-page">
    <div class="page-header">
      <h2>系统设置</h2>
      <el-button type="primary" :icon="Plus" @click="handleAdd">
        添加设置
      </el-button>
    </div>

    <!-- 设置表格 -->
    <el-table
      :data="settingList"
      v-loading="loading"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column prop="paramKey" label="参数键" width="200" />
      <el-table-column prop="paramValue" label="参数值" width="200" />
      <el-table-column prop="description" label="描述" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 设置表单对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="50%"
      destroy-on-close
    >
      <el-form
        ref="formRef"
        :model="settingForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="参数键" prop="paramKey">
          <el-input
            v-model="settingForm.paramKey"
            placeholder="请输入参数键"
            :disabled="formMode === 'edit'"
          />
        </el-form-item>
        <el-form-item label="参数值" prop="paramValue">
          <el-input
            v-model="settingForm.paramValue"
            placeholder="请输入参数值"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="settingForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入描述"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleSubmit"
            :loading="submitting"
          >
            确定
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import { getAllSettings, addSetting, updateSetting, deleteSetting } from '@/api/modules/setting';
import type { Setting } from '@/types';

// 数据列表
const settingList = ref<Setting[]>([]);
const loading = ref(false);

// 表单对话框相关
const dialogVisible = ref(false);
const formMode = ref<'add' | 'edit'>('add');
const selectedId = ref<number | undefined>(undefined);
const submitting = ref(false);

const dialogTitle = computed(() => {
  return formMode.value === 'add' ? '添加设置' : '编辑设置';
});

// 表单数据
const settingForm = reactive<Setting>({
  paramKey: '',
  paramValue: '',
  description: ''
});

// 表单验证规则
const formRules = {
  paramKey: [
    { required: true, message: '请输入参数键', trigger: 'blur' }
  ],
  paramValue: [
    { required: true, message: '请输入参数值', trigger: 'blur' }
  ]
};

const formRef = ref();

// 获取设置列表
const fetchSettingList = async () => {
  loading.value = true;
  try {
    const result = await getAllSettings();
    settingList.value = result;
  } catch (error: any) {
    ElMessage.error(error.msg || '获取设置列表失败');
  } finally {
    loading.value = false;
  }
};

// 处理添加
const handleAdd = () => {
  formMode.value = 'add';
  selectedId.value = undefined;
  Object.assign(settingForm, {
    paramKey: '',
    paramValue: '',
    description: ''
  });
  dialogVisible.value = true;
};

// 处理编辑
const handleEdit = (row: Setting) => {
  formMode.value = 'edit';
  selectedId.value = row.id;
  Object.assign(settingForm, {
    paramKey: row.paramKey,
    paramValue: row.paramValue,
    description: row.description || ''
  });
  dialogVisible.value = true;
};

// 处理删除
const handleDelete = async (row: Setting) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除设置 "${row.paramKey}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    await deleteSetting(row.id!);
    ElMessage.success('删除成功');
    fetchSettingList();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || '删除失败');
    }
  }
};

// 处理表单提交
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    if (formMode.value === 'add') {
      await addSetting(settingForm);
      ElMessage.success('添加成功');
    } else {
      await updateSetting(selectedId.value!, settingForm);
      ElMessage.success('更新成功');
    }

    dialogVisible.value = false;
    fetchSettingList();
  } catch (error: any) {
    if (error.msg) {
      ElMessage.error(error.msg);
    }
  } finally {
    submitting.value = false;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  fetchSettingList();
});
</script>

<style scoped>
.setting-list-page {
  padding: 24px;
  background-color: var(--bg-color-page);
  min-height: calc(100vh - 60px);
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  color: var(--text-color-primary);
  font-size: 24px;
  font-weight: 600;
}

.dialog-footer {
  text-align: right;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-start;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
}
</style>