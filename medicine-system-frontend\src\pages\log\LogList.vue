<template>
  <div class="log-list-page">
    <div class="page-header">
      <h2>日志管理</h2>
    </div>

    <!-- 搜索表单 -->
    <SearchForm
      :search-config="searchConfig"
      :initial-values="searchParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 数据表格 -->
    <el-table
      :data="logList"
      :loading="loading"
      stripe
      border
      style="width: 100%"
    >
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="userId" label="用户ID" width="100" />
      <el-table-column prop="username" label="用户名" width="120" />
      <el-table-column prop="action" label="操作" width="150" />
      <el-table-column prop="module" label="模块" width="120" />
      <el-table-column prop="detail" label="详情" min-width="200" show-overflow-tooltip />
      <el-table-column prop="ip" label="IP地址" width="140" />
      <el-table-column prop="createTime" label="操作时间" width="180">
        <template #default="{ row }">
          {{ formatDateTime(row.createTime || row.timestamp) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140" fixed="right">
        <template #default="{ row }">
          <div class="action-buttons">
            <el-button
              type="primary"
              size="small"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      style="margin-top: 20px; justify-content: center"
    />

    <!-- 查看详情对话框 -->
    <el-dialog
      v-model="detailDialogVisible"
      title="日志详情"
      width="600px"
    >
      <el-descriptions v-if="selectedLog" :column="1" border>
        <el-descriptions-item label="ID">{{ selectedLog.id }}</el-descriptions-item>
        <el-descriptions-item label="用户ID">{{ selectedLog.userId }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ selectedLog.username || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="操作">{{ selectedLog.action }}</el-descriptions-item>
        <el-descriptions-item label="模块">{{ selectedLog.module || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="详情">{{ selectedLog.detail || '无' }}</el-descriptions-item>
        <el-descriptions-item label="IP地址">{{ selectedLog.ip || '未知' }}</el-descriptions-item>
        <el-descriptions-item label="操作时间">
          {{ formatDateTime(selectedLog.createTime || selectedLog.timestamp) }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getAllLogs, deleteLog } from '@/api/modules/log';
import type { SystemLog, PageParams } from '@/types';
import SearchForm from '@/components/common/SearchForm.vue';

// 响应式数据
const loading = ref(false);
const logList = ref<SystemLog[]>([]);
const total = ref(0);
const currentPage = ref(1);
const pageSize = ref(20);

// 搜索参数
const searchParams = reactive<PageParams>({
  page: 1,
  size: 20
});

// 搜索配置
const searchConfig = {
  fields: [
    {
      key: 'userId',
      label: '用户ID',
      type: 'input',
      placeholder: '请输入用户ID'
    },
    {
      key: 'action',
      label: '操作',
      type: 'input',
      placeholder: '请输入操作名称'
    },
    {
      key: 'module',
      label: '模块',
      type: 'input',
      placeholder: '请输入模块名称'
    },
    {
      key: 'startDate',
      label: '开始时间',
      type: 'date',
      placeholder: '请选择开始时间'
    },
    {
      key: 'endDate',
      label: '结束时间',
      type: 'date',
      placeholder: '请选择结束时间'
    }
  ]
};

// 详情对话框
const detailDialogVisible = ref(false);
const selectedLog = ref<SystemLog | null>(null);

// 格式化日期时间
const formatDateTime = (dateTime: string | Date | undefined) => {
  if (!dateTime) return '未知';
  const date = new Date(dateTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
};

// 获取日志列表
const fetchLogList = async () => {
  loading.value = true;
  try {
    const result = await getAllLogs();

    // 处理返回的数据结构
    if (Array.isArray(result)) {
      logList.value = result;
      total.value = result.length;
    } else {
      logList.value = result.records || [];
      total.value = result.total || 0;
    }
  } catch (error: any) {
    ElMessage.error(error.msg || '获取日志列表失败');
    console.error('获取日志列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 处理搜索
const handleSearch = (params: Record<string, any>) => {
  Object.keys(params).forEach(key => {
    searchParams[key as keyof PageParams] = params[key];
  });
  currentPage.value = 1;
  fetchLogList();
};

// 处理重置
const handleReset = () => {
  currentPage.value = 1;
  Object.keys(searchParams).forEach(key => {
    if (key !== 'page' && key !== 'size') {
      delete searchParams[key as keyof PageParams];
    }
  });
  fetchLogList();
};

// 处理查看详情
const handleView = (row: SystemLog) => {
  selectedLog.value = row;
  detailDialogVisible.value = true;
};

// 处理删除
const handleDelete = async (row: SystemLog) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除这条日志记录吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    await deleteLog(row.id!);
    ElMessage.success('删除成功');
    fetchLogList();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.msg || '删除失败');
    }
  }
};

// 监听分页变化
watch([currentPage, pageSize], () => {
  searchParams.page = currentPage.value;
  searchParams.size = pageSize.value;
  fetchLogList();
});

// 页面加载时获取数据
onMounted(() => {
  fetchLogList();
});
</script>

<style scoped>
.log-list-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  min-width: 60px;
}

.action-buttons .el-button + .el-button {
  margin-left: 0;
}
</style>