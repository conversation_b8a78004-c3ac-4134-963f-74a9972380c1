2025-08-03 18:26:53.786  INFO 18712 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 18712 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 18:26:53.793 DEBUG 18712 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 18:26:53.794  INFO 18712 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 18:26:53.883  INFO 18712 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 18:26:53.883  INFO 18712 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 18:26:54.853  INFO 18712 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 18:26:54.974  INFO 18712 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 110 ms. Found 12 JPA repository interfaces.
2025-08-03 18:26:55.812  INFO 18712 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 18:26:55.824  INFO 18712 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 18:26:55.825  INFO 18712 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 18:26:55.919  INFO 18712 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 18:26:55.919  INFO 18712 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2035 ms
2025-08-03 18:26:56.182  INFO 18712 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 18:26:56.261  INFO 18712 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 18:26:56.500  INFO 18712 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 18:26:56.656  INFO 18712 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 18:26:57.336  INFO 18712 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 18:26:57.360  INFO 18712 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 18:26:58.463  INFO 18712 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 18:26:58.476  INFO 18712 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 18:26:58.539  WARN 18712 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 18:26:59.576  WARN 18712 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: a0921846-b5b3-4e42-8fcf-0c1a17e30627

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 18:26:59.745  INFO 18712 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@500edf46, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@690e882a, org.springframework.security.web.context.SecurityContextPersistenceFilter@412cf680, org.springframework.security.web.header.HeaderWriterFilter@25158332, org.springframework.web.filter.CorsFilter@2fe9d75a, org.springframework.security.web.authentication.logout.LogoutFilter@1854deb1, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4157de22, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@371fb93, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@1eb45e28, org.springframework.security.web.session.SessionManagementFilter@54f3b3d, org.springframework.security.web.access.ExceptionTranslationFilter@3b1727cd, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@23706aee]
2025-08-03 18:27:00.247  INFO 18712 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 18:27:00.292  INFO 18712 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 18:27:00.305  INFO 18712 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 7.052 seconds (JVM running for 12.378)
2025-08-03 18:27:33.373  INFO 18712 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 18:27:33.373  INFO 18712 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 18:27:33.375  INFO 18712 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-03 18:35:27.404  INFO 18712 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 18:35:27.430  INFO 18712 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 18:35:27.454  INFO 18712 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 18:35:46.991  INFO 3240 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 3240 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 18:35:46.993 DEBUG 3240 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 18:35:46.994  INFO 3240 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 18:35:47.086  INFO 3240 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 18:35:47.087  INFO 3240 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 18:35:49.276  INFO 3240 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 18:35:49.459  INFO 3240 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 170 ms. Found 12 JPA repository interfaces.
2025-08-03 18:35:50.450  INFO 3240 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 18:35:50.467  INFO 3240 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 18:35:50.468  INFO 3240 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 18:35:50.581  INFO 3240 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 18:35:50.582  INFO 3240 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3494 ms
2025-08-03 18:35:50.908  INFO 3240 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 18:35:50.979  INFO 3240 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 18:35:51.236  INFO 3240 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 18:35:51.387  INFO 3240 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 18:35:51.938  INFO 3240 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 18:35:51.955  INFO 3240 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 18:35:53.182  INFO 3240 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 18:35:53.195  INFO 3240 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 18:35:53.262  WARN 3240 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 18:35:54.520  WARN 3240 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 91625828-b55e-4b09-ae65-534986213e6a

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 18:35:54.676  INFO 3240 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@46640289, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3ceab1b2, org.springframework.security.web.context.SecurityContextPersistenceFilter@22bf185c, org.springframework.security.web.header.HeaderWriterFilter@31f681d, org.springframework.web.filter.CorsFilter@683289b1, org.springframework.security.web.authentication.logout.LogoutFilter@2b8e77ed, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@41e4c0fd, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@21e45631, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2c080550, org.springframework.security.web.session.SessionManagementFilter@1e95eaed, org.springframework.security.web.access.ExceptionTranslationFilter@618b1932, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7854c9bf]
2025-08-03 18:35:55.306  INFO 3240 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 18:35:55.383  INFO 3240 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 18:35:55.403  INFO 3240 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 8.966 seconds (JVM running for 10.739)
2025-08-03 18:35:56.201  INFO 3240 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 18:35:56.201  INFO 3240 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 18:35:56.204  INFO 3240 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-03 18:41:57.990  INFO 3240 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 18:41:57.998  INFO 3240 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 18:41:58.015  INFO 3240 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 18:42:14.369  INFO 19112 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 19112 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 18:42:14.370 DEBUG 19112 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 18:42:14.371  INFO 19112 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 18:42:14.452  INFO 19112 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 18:42:14.452  INFO 19112 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 18:42:16.654  INFO 19112 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 18:42:16.831  INFO 19112 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 162 ms. Found 12 JPA repository interfaces.
2025-08-03 18:42:17.754  INFO 19112 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 18:42:17.770  INFO 19112 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 18:42:17.770  INFO 19112 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 18:42:17.889  INFO 19112 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 18:42:17.889  INFO 19112 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 3436 ms
2025-08-03 18:42:18.166  INFO 19112 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 18:42:18.246  INFO 19112 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 18:42:18.466  INFO 19112 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 18:42:18.604  INFO 19112 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 18:42:19.146  INFO 19112 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 18:42:19.163  INFO 19112 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 18:42:20.149  INFO 19112 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 18:42:20.160  INFO 19112 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 18:42:20.238  WARN 19112 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 18:42:21.201  WARN 19112 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 7c5f1331-166b-41ea-9b28-3e3e4f9fdb6f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 18:42:21.336  INFO 19112 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1fff10d5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@16b1cef7, org.springframework.security.web.context.SecurityContextPersistenceFilter@13226a50, org.springframework.security.web.header.HeaderWriterFilter@331528b7, org.springframework.web.filter.CorsFilter@b624ca0, org.springframework.security.web.authentication.logout.LogoutFilter@7040fac, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@ffd75a, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3eee1dc3, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@69d7bbe6, org.springframework.security.web.session.SessionManagementFilter@750f5f20, org.springframework.security.web.access.ExceptionTranslationFilter@408fc75c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7a708ccd]
2025-08-03 18:42:21.756  INFO 19112 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 18:42:21.798  INFO 19112 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 18:42:21.809  INFO 19112 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 7.974 seconds (JVM running for 9.409)
2025-08-03 18:42:36.353  INFO 19112 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 18:42:36.353  INFO 19112 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 18:42:36.355  INFO 19112 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-03 19:09:26.329  INFO 19112 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 19:09:26.338  INFO 19112 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 19:09:26.355  INFO 19112 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 19:09:36.058  INFO 19720 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 19720 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 19:09:36.060 DEBUG 19720 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 19:09:36.061  INFO 19720 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 19:09:36.162  INFO 19720 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 19:09:36.162  INFO 19720 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 19:09:37.341  INFO 19720 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 19:09:37.523  INFO 19720 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 169 ms. Found 12 JPA repository interfaces.
2025-08-03 19:09:38.505  INFO 19720 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 19:09:38.517  INFO 19720 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 19:09:38.519  INFO 19720 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 19:09:38.627  INFO 19720 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 19:09:38.627  INFO 19720 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2464 ms
2025-08-03 19:09:38.908  INFO 19720 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 19:09:38.978  INFO 19720 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 19:09:39.190  INFO 19720 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 19:09:39.303  INFO 19720 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 19:09:39.770  INFO 19720 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 19:09:39.793  INFO 19720 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 19:09:40.815  INFO 19720 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 19:09:40.825  INFO 19720 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 19:09:40.884  WARN 19720 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 19:09:41.814  WARN 19720 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: c93587ea-eafd-47ba-bc55-d3bead087f82

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 19:09:41.948  INFO 19720 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@f5d7475, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7660d94, org.springframework.security.web.context.SecurityContextPersistenceFilter@47805202, org.springframework.security.web.header.HeaderWriterFilter@1949a247, org.springframework.web.filter.CorsFilter@3255f4ce, org.springframework.security.web.authentication.logout.LogoutFilter@5910adc5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@6987021d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6c335400, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6c10df84, org.springframework.security.web.session.SessionManagementFilter@364f883a, org.springframework.security.web.access.ExceptionTranslationFilter@2627f4c5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@31317cbe]
2025-08-03 19:09:42.342  INFO 19720 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 19:09:42.384  INFO 19720 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 19:09:42.394  INFO 19720 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.908 seconds (JVM running for 8.197)
2025-08-03 19:10:14.378  INFO 19720 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 19:10:14.379  INFO 19720 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 19:10:14.386  INFO 19720 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 6 ms
2025-08-03 19:22:31.227  INFO 19720 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 19:22:31.230  INFO 19720 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 19:22:31.241  INFO 19720 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 19:22:41.028  INFO 16936 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 16936 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 19:22:41.030 DEBUG 16936 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 19:22:41.031  INFO 16936 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 19:22:41.113  INFO 16936 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 19:22:41.113  INFO 16936 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 19:22:42.176  INFO 16936 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 19:22:42.308  INFO 16936 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 118 ms. Found 12 JPA repository interfaces.
2025-08-03 19:22:42.961  INFO 16936 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 19:22:42.972  INFO 16936 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 19:22:42.972  INFO 16936 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 19:22:43.041  INFO 16936 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 19:22:43.041  INFO 16936 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1927 ms
2025-08-03 19:22:43.253  INFO 16936 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 19:22:43.328  INFO 16936 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 19:22:43.489  INFO 16936 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 19:22:43.595  INFO 16936 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 19:22:44.067  INFO 16936 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 19:22:44.083  INFO 16936 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 19:22:44.946  INFO 16936 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 19:22:44.955  INFO 16936 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 19:22:45.009  WARN 16936 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 19:22:45.857  WARN 16936 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 98f70e00-f6b6-4b27-9e78-e9bc93d0f61f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 19:22:45.996  INFO 16936 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@50bd7d38, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1f21389c, org.springframework.security.web.context.SecurityContextPersistenceFilter@1624d786, org.springframework.security.web.header.HeaderWriterFilter@36bc087a, org.springframework.web.filter.CorsFilter@529ea720, org.springframework.security.web.authentication.logout.LogoutFilter@5b445180, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@72ffb3ca, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@21833ec1, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6f280d42, org.springframework.security.web.session.SessionManagementFilter@71f1828, org.springframework.security.web.access.ExceptionTranslationFilter@617965a9, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7893f0ea]
2025-08-03 19:22:46.458  INFO 16936 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 19:22:46.512  INFO 16936 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 19:22:46.525  INFO 16936 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.048 seconds (JVM running for 6.998)
2025-08-03 19:23:04.003  INFO 16936 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 19:23:04.003  INFO 16936 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 19:23:04.005  INFO 16936 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-03 19:29:39.862  INFO 16936 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 19:29:39.869  INFO 16936 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 19:29:39.877  INFO 16936 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 19:29:47.650  INFO 11408 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 11408 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 19:29:47.651 DEBUG 11408 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 19:29:47.652  INFO 11408 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 19:29:47.740  INFO 11408 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 19:29:47.741  INFO 11408 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 19:29:48.744  INFO 11408 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 19:29:48.856  INFO 11408 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 100 ms. Found 12 JPA repository interfaces.
2025-08-03 19:29:49.504  INFO 11408 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 19:29:49.516  INFO 11408 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 19:29:49.517  INFO 11408 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 19:29:49.587  INFO 11408 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 19:29:49.588  INFO 11408 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1845 ms
2025-08-03 19:29:49.823  INFO 11408 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 19:29:49.875  INFO 11408 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 19:29:50.046  INFO 11408 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 19:29:50.149  INFO 11408 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 19:29:50.563  INFO 11408 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 19:29:50.578  INFO 11408 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 19:29:51.461  INFO 11408 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 19:29:51.474  INFO 11408 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 19:29:51.529  WARN 11408 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 19:29:52.507  WARN 11408 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: e9b1d9b5-1ce8-41eb-8bb8-3358b519f45d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 19:29:52.638  INFO 11408 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@5b9fd249, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@34213110, org.springframework.security.web.context.SecurityContextPersistenceFilter@187b499a, org.springframework.security.web.header.HeaderWriterFilter@22872cc4, org.springframework.web.filter.CorsFilter@5373a904, org.springframework.security.web.authentication.logout.LogoutFilter@481a7f13, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4a48b664, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2f775755, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@538a1c1f, org.springframework.security.web.session.SessionManagementFilter@33759787, org.springframework.security.web.access.ExceptionTranslationFilter@f978a51, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@54c7f922]
2025-08-03 19:29:53.018  INFO 11408 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 19:29:53.068  INFO 11408 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 19:29:53.081  INFO 11408 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.976 seconds (JVM running for 7.078)
2025-08-03 19:30:07.494  INFO 11408 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 19:30:07.494  INFO 11408 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 19:30:07.494  INFO 11408 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-08-03 19:40:25.839  INFO 11408 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 19:40:25.846  INFO 11408 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 19:40:25.858  INFO 11408 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 19:40:33.879  INFO 1192 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 1192 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 19:40:33.880 DEBUG 1192 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 19:40:33.881  INFO 1192 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 19:40:33.966  INFO 1192 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 19:40:33.967  INFO 1192 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 19:40:34.983  INFO 1192 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 19:40:35.093  INFO 1192 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 98 ms. Found 12 JPA repository interfaces.
2025-08-03 19:40:35.775  INFO 1192 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 19:40:35.784  INFO 1192 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 19:40:35.784  INFO 1192 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 19:40:35.901  INFO 1192 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 19:40:35.902  INFO 1192 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1935 ms
2025-08-03 19:40:36.130  INFO 1192 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 19:40:36.181  INFO 1192 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 19:40:36.344  INFO 1192 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 19:40:36.459  INFO 1192 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 19:40:36.866  INFO 1192 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 19:40:36.881  INFO 1192 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 19:40:37.855  INFO 1192 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 19:40:37.866  INFO 1192 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 19:40:37.925  WARN 1192 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 19:40:38.843  WARN 1192 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: d5f47b9d-d395-49c5-9299-414403b1ee8b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 19:40:39.017  INFO 1192 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@756b101b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@32f7d7b9, org.springframework.security.web.context.SecurityContextPersistenceFilter@64ef1ce, org.springframework.security.web.header.HeaderWriterFilter@5f010079, org.springframework.web.filter.CorsFilter@40ed75ba, org.springframework.security.web.authentication.logout.LogoutFilter@559079f4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@68bfa38f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@339462db, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@554e0784, org.springframework.security.web.session.SessionManagementFilter@1994fd0f, org.springframework.security.web.access.ExceptionTranslationFilter@566f9352, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7d3d3fef]
2025-08-03 19:40:39.493  INFO 1192 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 19:40:39.537  INFO 1192 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 19:40:39.550  INFO 1192 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.215 seconds (JVM running for 7.237)
2025-08-03 19:41:00.031  INFO 1192 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 19:41:00.031  INFO 1192 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 19:41:00.031  INFO 1192 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 0 ms
2025-08-03 19:58:18.699  INFO 1192 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 19:58:18.703  INFO 1192 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 19:58:18.712  INFO 1192 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 19:58:27.032  INFO 2104 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 2104 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 19:58:27.034 DEBUG 2104 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 19:58:27.034  INFO 2104 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 19:58:27.128  INFO 2104 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 19:58:27.128  INFO 2104 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 19:58:28.067  INFO 2104 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 19:58:28.206  INFO 2104 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 129 ms. Found 12 JPA repository interfaces.
2025-08-03 19:58:29.116  INFO 2104 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 19:58:29.129  INFO 2104 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 19:58:29.129  INFO 2104 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 19:58:29.215  INFO 2104 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 19:58:29.215  INFO 2104 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2086 ms
2025-08-03 19:58:29.430  INFO 2104 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 19:58:29.486  INFO 2104 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 19:58:29.664  INFO 2104 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 19:58:29.777  INFO 2104 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 19:58:30.263  INFO 2104 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 19:58:30.282  INFO 2104 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 19:58:31.346  INFO 2104 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 19:58:31.363  INFO 2104 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 19:58:31.464  WARN 2104 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 19:58:32.542  WARN 2104 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: ffab05c6-25e0-40b1-a991-66e123bbad0c

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 19:58:32.678  INFO 2104 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@51eb27c0, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@60312442, org.springframework.security.web.context.SecurityContextPersistenceFilter@3d2edee6, org.springframework.security.web.header.HeaderWriterFilter@23c48006, org.springframework.web.filter.CorsFilter@3c8f7a9d, org.springframework.security.web.authentication.logout.LogoutFilter@12c43ac6, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1994fd0f, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@68bfa38f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@14ccd481, org.springframework.security.web.session.SessionManagementFilter@2c64a775, org.springframework.security.web.access.ExceptionTranslationFilter@3155c8e5, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@23b01891]
2025-08-03 19:58:33.035  INFO 2104 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 19:58:33.075  INFO 2104 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 19:58:33.086  INFO 2104 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.641 seconds (JVM running for 7.758)
2025-08-03 19:58:36.388  INFO 2104 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 19:58:36.389  INFO 2104 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 19:58:36.393  INFO 2104 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-08-03 20:02:41.036  INFO 2104 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:02:41.045  INFO 2104 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 20:02:41.055  INFO 2104 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 20:02:48.783  INFO 3468 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 3468 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 20:02:48.785 DEBUG 3468 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 20:02:48.785  INFO 3468 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 20:02:48.862  INFO 3468 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 20:02:48.863  INFO 3468 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 20:02:49.717  INFO 3468 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 20:02:49.848  INFO 3468 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 122 ms. Found 12 JPA repository interfaces.
2025-08-03 20:02:50.613  INFO 3468 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 20:02:50.623  INFO 3468 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 20:02:50.623  INFO 3468 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 20:02:50.690  INFO 3468 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 20:02:50.690  INFO 3468 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1826 ms
2025-08-03 20:02:50.789  INFO 3468 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 20:02:51.184  INFO 3468 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 20:02:51.295  INFO 3468 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 20:02:51.350  INFO 3468 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 20:02:51.537  INFO 3468 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 20:02:51.632  INFO 3468 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 20:02:52.425  INFO 3468 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 20:02:52.436  INFO 3468 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:02:52.489  WARN 3468 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 20:02:53.328  WARN 3468 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 68de3f20-9783-41cf-b770-7f6805cb70d6

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 20:02:53.459  INFO 3468 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@43d8e570, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5082bd3d, org.springframework.security.web.context.SecurityContextPersistenceFilter@3bb40bcf, org.springframework.security.web.header.HeaderWriterFilter@3996a5ad, org.springframework.web.filter.CorsFilter@1e5b7342, org.springframework.security.web.authentication.logout.LogoutFilter@3189a83f, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@498ed5c1, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@787d755c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@13a0438e, org.springframework.security.web.session.SessionManagementFilter@76e7b294, org.springframework.security.web.access.ExceptionTranslationFilter@1b207683, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1e79db80]
2025-08-03 20:02:53.785  INFO 3468 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 20:02:53.822  INFO 3468 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 20:02:53.834  INFO 3468 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.556 seconds (JVM running for 6.658)
2025-08-03 20:02:58.966  INFO 3468 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 20:02:58.966  INFO 3468 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 20:02:58.968  INFO 3468 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-03 20:03:48.222  WARN 3468 --- [http-nio-8080-exec-2] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:03:48.223 ERROR 3468 --- [http-nio-8080-exec-2] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:03:48.224  INFO 3468 --- [http-nio-8080-exec-2] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:03:56.036  WARN 3468 --- [http-nio-8080-exec-7] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:03:56.036 ERROR 3468 --- [http-nio-8080-exec-7] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:03:56.036  INFO 3468 --- [http-nio-8080-exec-7] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:03:59.212  WARN 3468 --- [http-nio-8080-exec-5] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:03:59.212 ERROR 3468 --- [http-nio-8080-exec-5] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:03:59.212  INFO 3468 --- [http-nio-8080-exec-5] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:04:03.241  WARN 3468 --- [http-nio-8080-exec-9] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:04:03.242 ERROR 3468 --- [http-nio-8080-exec-9] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:04:03.242  INFO 3468 --- [http-nio-8080-exec-9] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:04:10.494  WARN 3468 --- [http-nio-8080-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:04:10.494 ERROR 3468 --- [http-nio-8080-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:04:10.494  INFO 3468 --- [http-nio-8080-exec-4] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:07:01.109  WARN 3468 --- [http-nio-8080-exec-8] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:07:01.110 ERROR 3468 --- [http-nio-8080-exec-8] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:07:01.110  INFO 3468 --- [http-nio-8080-exec-8] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:07:03.789  WARN 3468 --- [http-nio-8080-exec-6] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:07:03.789 ERROR 3468 --- [http-nio-8080-exec-6] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:07:03.789  INFO 3468 --- [http-nio-8080-exec-6] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:10:20.547  INFO 3468 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:10:20.552  INFO 3468 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 20:10:20.563  INFO 3468 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 20:10:28.408  INFO 6800 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 6800 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 20:10:28.410 DEBUG 6800 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 20:10:28.411  INFO 6800 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 20:10:28.495  INFO 6800 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 20:10:28.495  INFO 6800 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 20:10:29.458  INFO 6800 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 20:10:29.581  INFO 6800 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 113 ms. Found 12 JPA repository interfaces.
2025-08-03 20:10:30.282  INFO 6800 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 20:10:30.291  INFO 6800 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 20:10:30.291  INFO 6800 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 20:10:30.376  INFO 6800 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 20:10:30.377  INFO 6800 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1881 ms
2025-08-03 20:10:30.522  INFO 6800 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 20:10:31.013  INFO 6800 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 20:10:31.135  INFO 6800 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 20:10:31.197  INFO 6800 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 20:10:31.410  INFO 6800 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 20:10:31.516  INFO 6800 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 20:10:32.291  INFO 6800 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 20:10:32.300  INFO 6800 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:10:32.357  WARN 6800 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 20:10:33.175  WARN 6800 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 429fa5cf-af32-4e60-b43d-b2ea6cce2277

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 20:10:33.307  INFO 6800 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@2d3de812, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@6ddd5f3b, org.springframework.security.web.context.SecurityContextPersistenceFilter@9d36be, org.springframework.security.web.header.HeaderWriterFilter@c68a8f6, org.springframework.web.filter.CorsFilter@1d52d6e4, org.springframework.security.web.authentication.logout.LogoutFilter@59e9d73c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@b646da4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@367eaf9a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@11d023d0, org.springframework.security.web.session.SessionManagementFilter@1e6ffe4c, org.springframework.security.web.access.ExceptionTranslationFilter@61298e7d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@435f3cdd]
2025-08-03 20:10:33.725  INFO 6800 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 20:10:33.770  INFO 6800 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 20:10:33.781  INFO 6800 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.918 seconds (JVM running for 6.882)
2025-08-03 20:11:13.042  INFO 6800 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 20:11:13.043  INFO 6800 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 20:11:13.046  INFO 6800 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-03 20:11:13.167  WARN 6800 --- [http-nio-8080-exec-1] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.lang.Integer` from String "ACTIVE": not a valid `java.lang.Integer` value; nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.lang.Integer` from String "ACTIVE": not a valid `java.lang.Integer` value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 41] (through reference chain: com.example.medicine.entity.User["status"])]
2025-08-03 20:11:18.579  WARN 6800 --- [http-nio-8080-exec-7] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.lang.Integer` from String "ACTIVE": not a valid `java.lang.Integer` value; nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.lang.Integer` from String "ACTIVE": not a valid `java.lang.Integer` value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 41] (through reference chain: com.example.medicine.entity.User["status"])]
2025-08-03 20:11:27.593  INFO 6800 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:11:27.598  INFO 6800 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 20:11:27.610  INFO 6800 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 20:11:34.644  INFO 21176 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 21176 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 20:11:34.646 DEBUG 21176 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 20:11:34.649  INFO 21176 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 20:11:34.742  INFO 21176 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 20:11:34.742  INFO 21176 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 20:11:35.892  INFO 21176 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 20:11:36.043  INFO 21176 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 139 ms. Found 12 JPA repository interfaces.
2025-08-03 20:11:36.971  INFO 21176 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 20:11:36.982  INFO 21176 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 20:11:36.983  INFO 21176 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 20:11:37.067  INFO 21176 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 20:11:37.068  INFO 21176 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2325 ms
2025-08-03 20:11:37.186  INFO 21176 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 20:11:37.618  INFO 21176 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 20:11:37.726  INFO 21176 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 20:11:37.783  INFO 21176 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 20:11:37.974  INFO 21176 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 20:11:38.086  INFO 21176 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 20:11:38.828  INFO 21176 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 20:11:38.838  INFO 21176 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:11:38.891  WARN 21176 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 20:11:39.846  WARN 21176 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 11a10322-bcaa-4ff2-ad8f-294e5bd8fe7f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 20:11:39.948  INFO 21176 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3fc9c9c6, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@797063ef, org.springframework.security.web.context.SecurityContextPersistenceFilter@4ab644a, org.springframework.security.web.header.HeaderWriterFilter@4a3d0bd, org.springframework.web.filter.CorsFilter@65c1103f, org.springframework.security.web.authentication.logout.LogoutFilter@56b97df8, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1c1267df, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@40fcd18a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7009a320, org.springframework.security.web.session.SessionManagementFilter@2a96dda1, org.springframework.security.web.access.ExceptionTranslationFilter@612153bc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@420b5355]
2025-08-03 20:11:40.314  INFO 21176 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 20:11:40.352  INFO 21176 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 20:11:40.363  INFO 21176 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.6 seconds (JVM running for 10.752)
2025-08-03 20:11:45.112  INFO 21176 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 20:11:45.112  INFO 21176 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 20:11:45.116  INFO 21176 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-08-03 20:11:48.035  WARN 21176 --- [http-nio-8080-exec-6] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.lang.Integer` from String "ACTIVE": not a valid `java.lang.Integer` value; nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.lang.Integer` from String "ACTIVE": not a valid `java.lang.Integer` value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 41] (through reference chain: com.example.medicine.entity.User["status"])]
2025-08-03 20:11:56.288  WARN 21176 --- [http-nio-8080-exec-1] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.lang.Integer` from String "INACTIVE": not a valid `java.lang.Integer` value; nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.lang.Integer` from String "INACTIVE": not a valid `java.lang.Integer` value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 41] (through reference chain: com.example.medicine.entity.User["status"])]
2025-08-03 20:11:58.317  WARN 21176 --- [http-nio-8080-exec-2] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.lang.Integer` from String "INACTIVE": not a valid `java.lang.Integer` value; nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.lang.Integer` from String "INACTIVE": not a valid `java.lang.Integer` value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 41] (through reference chain: com.example.medicine.entity.User["status"])]
2025-08-03 20:12:02.554  WARN 21176 --- [http-nio-8080-exec-3] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.lang.Integer` from String "INACTIVE": not a valid `java.lang.Integer` value; nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.lang.Integer` from String "INACTIVE": not a valid `java.lang.Integer` value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 41] (through reference chain: com.example.medicine.entity.User["status"])]
2025-08-03 20:13:35.607  WARN 21176 --- [http-nio-8080-exec-6] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.lang.Integer` from String "INACTIVE": not a valid `java.lang.Integer` value; nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.lang.Integer` from String "INACTIVE": not a valid `java.lang.Integer` value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 41] (through reference chain: com.example.medicine.entity.User["status"])]
2025-08-03 20:16:15.582  WARN 21176 --- [http-nio-8080-exec-2] .w.s.m.s.DefaultHandlerExceptionResolver : Resolved [org.springframework.http.converter.HttpMessageNotReadableException: JSON parse error: Cannot deserialize value of type `java.lang.Integer` from String "INACTIVE": not a valid `java.lang.Integer` value; nested exception is com.fasterxml.jackson.databind.exc.InvalidFormatException: Cannot deserialize value of type `java.lang.Integer` from String "INACTIVE": not a valid `java.lang.Integer` value<EOL> at [Source: (org.springframework.util.StreamUtils$NonClosingInputStream); line: 1, column: 41] (through reference chain: com.example.medicine.entity.User["status"])]
2025-08-03 20:20:50.721  WARN 21176 --- [http-nio-8080-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:20:50.721 ERROR 21176 --- [http-nio-8080-exec-4] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:20:50.722  INFO 21176 --- [http-nio-8080-exec-4] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:21:47.332  WARN 21176 --- [http-nio-8080-exec-7] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:21:47.333 ERROR 21176 --- [http-nio-8080-exec-7] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:21:47.333  INFO 21176 --- [http-nio-8080-exec-7] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:22:46.344  WARN 21176 --- [http-nio-8080-exec-5] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:22:46.344 ERROR 21176 --- [http-nio-8080-exec-5] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:22:46.344  INFO 21176 --- [http-nio-8080-exec-5] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:23:44.057  INFO 21176 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:23:44.062  INFO 21176 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 20:23:44.072  INFO 21176 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 20:23:51.838  INFO 11640 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 11640 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 20:23:51.839 DEBUG 11640 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 20:23:51.840  INFO 11640 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 20:23:51.906  INFO 11640 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 20:23:51.907  INFO 11640 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 20:23:52.965  INFO 11640 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 20:23:53.073  INFO 11640 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 99 ms. Found 12 JPA repository interfaces.
2025-08-03 20:23:53.688  INFO 11640 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 20:23:53.698  INFO 11640 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 20:23:53.698  INFO 11640 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 20:23:53.766  INFO 11640 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 20:23:53.766  INFO 11640 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1858 ms
2025-08-03 20:23:53.860  INFO 11640 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 20:23:54.264  INFO 11640 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 20:23:54.407  INFO 11640 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 20:23:54.480  INFO 11640 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 20:23:54.660  INFO 11640 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 20:23:54.758  INFO 11640 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 20:23:55.479  INFO 11640 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 20:23:55.488  INFO 11640 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:23:55.529  WARN 11640 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 20:23:56.327  WARN 11640 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: e4ef6567-c454-460e-9660-d9c610cc12f2

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 20:23:56.441  INFO 11640 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@384ba42c, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@47917cda, org.springframework.security.web.context.SecurityContextPersistenceFilter@18055676, org.springframework.security.web.header.HeaderWriterFilter@63808ecc, org.springframework.web.filter.CorsFilter@595b3ea, org.springframework.security.web.authentication.logout.LogoutFilter@edd23d5, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@2fe8e042, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2f4e8055, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@cba2b7c, org.springframework.security.web.session.SessionManagementFilter@73414381, org.springframework.security.web.access.ExceptionTranslationFilter@60bae910, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3460e585]
2025-08-03 20:23:56.754  INFO 11640 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 20:23:56.787  INFO 11640 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 20:23:56.797  INFO 11640 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.355 seconds (JVM running for 9.861)
2025-08-03 20:24:09.524  INFO 11640 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 20:24:09.526  INFO 11640 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 20:24:09.530  INFO 11640 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-03 20:24:18.056  WARN 11640 --- [http-nio-8080-exec-7] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:24:18.057 ERROR 11640 --- [http-nio-8080-exec-7] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:24:18.058  INFO 11640 --- [http-nio-8080-exec-7] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:24:26.948  WARN 11640 --- [http-nio-8080-exec-10] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:24:26.948 ERROR 11640 --- [http-nio-8080-exec-10] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:24:26.949  INFO 11640 --- [http-nio-8080-exec-10] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:28:54.268  WARN 11640 --- [http-nio-8080-exec-6] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:28:54.268 ERROR 11640 --- [http-nio-8080-exec-6] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:28:54.268  INFO 11640 --- [http-nio-8080-exec-6] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:32:05.870  WARN 11640 --- [http-nio-8080-exec-9] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:32:05.871 ERROR 11640 --- [http-nio-8080-exec-9] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:32:05.872  INFO 11640 --- [http-nio-8080-exec-9] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:35:24.133  WARN 11640 --- [http-nio-8080-exec-2] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:35:24.133 ERROR 11640 --- [http-nio-8080-exec-2] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:35:24.133  INFO 11640 --- [http-nio-8080-exec-2] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:38:19.724  WARN 11640 --- [http-nio-8080-exec-5] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:38:19.724 ERROR 11640 --- [http-nio-8080-exec-5] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:38:19.727  INFO 11640 --- [http-nio-8080-exec-5] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:38:45.568  WARN 11640 --- [http-nio-8080-exec-2] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:38:45.568 ERROR 11640 --- [http-nio-8080-exec-2] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:38:45.568  INFO 11640 --- [http-nio-8080-exec-2] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:40:06.228  WARN 11640 --- [http-nio-8080-exec-7] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1048, SQLState: 23000
2025-08-03 20:40:06.228 ERROR 11640 --- [http-nio-8080-exec-7] o.h.engine.jdbc.spi.SqlExceptionHelper   : Column 'password' cannot be null
2025-08-03 20:40:06.229  INFO 11640 --- [http-nio-8080-exec-7] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
2025-08-03 20:40:24.638  INFO 11640 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:40:24.641  INFO 11640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 20:40:24.647  INFO 11640 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 20:40:33.546  INFO 19120 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 19120 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 20:40:33.548 DEBUG 19120 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 20:40:33.549  INFO 19120 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 20:40:33.624  INFO 19120 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 20:40:33.624  INFO 19120 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 20:40:34.655  INFO 19120 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 20:40:34.798  INFO 19120 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 125 ms. Found 12 JPA repository interfaces.
2025-08-03 20:40:35.530  INFO 19120 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 20:40:35.539  INFO 19120 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 20:40:35.540  INFO 19120 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 20:40:35.624  INFO 19120 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 20:40:35.624  INFO 19120 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1999 ms
2025-08-03 20:40:35.777  INFO 19120 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 20:40:36.259  INFO 19120 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 20:40:36.463  INFO 19120 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 20:40:36.543  INFO 19120 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 20:40:36.801  INFO 19120 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 20:40:36.945  INFO 19120 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 20:40:37.810  INFO 19120 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 20:40:37.820  INFO 19120 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:40:37.875  WARN 19120 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 20:40:38.837  WARN 19120 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 76c0ae71-c05c-4411-97aa-dd9c21ced801

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 20:40:38.956  INFO 19120 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1c25c470, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3d64f28, org.springframework.security.web.context.SecurityContextPersistenceFilter@7e458e67, org.springframework.security.web.header.HeaderWriterFilter@4181e323, org.springframework.web.filter.CorsFilter@1843d167, org.springframework.security.web.authentication.logout.LogoutFilter@195e8e40, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@61298e7d, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@6c124e8a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2138188b, org.springframework.security.web.session.SessionManagementFilter@4b2200cb, org.springframework.security.web.access.ExceptionTranslationFilter@6de87cfb, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@68ec6f43]
2025-08-03 20:40:39.300  INFO 19120 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 20:40:39.337  INFO 19120 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 20:40:39.345  INFO 19120 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.351 seconds (JVM running for 7.42)
2025-08-03 20:41:02.976  INFO 19120 --- [http-nio-8080-exec-2] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 20:41:02.977  INFO 19120 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 20:41:02.978  INFO 19120 --- [http-nio-8080-exec-2] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-03 20:59:04.077  INFO 19120 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:59:04.081  INFO 19120 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 20:59:04.089  INFO 19120 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 20:59:12.061  INFO 15152 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 15152 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 20:59:12.062 DEBUG 15152 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 20:59:12.064  INFO 15152 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 20:59:12.157  INFO 15152 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 20:59:12.157  INFO 15152 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 20:59:13.155  INFO 15152 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 20:59:13.289  INFO 15152 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 125 ms. Found 12 JPA repository interfaces.
2025-08-03 20:59:13.963  INFO 15152 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 20:59:13.973  INFO 15152 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 20:59:13.973  INFO 15152 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 20:59:14.045  INFO 15152 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 20:59:14.045  INFO 15152 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1887 ms
2025-08-03 20:59:14.140  INFO 15152 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 20:59:14.582  INFO 15152 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 20:59:14.693  INFO 15152 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 20:59:14.748  INFO 15152 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 20:59:14.947  INFO 15152 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 20:59:15.140  INFO 15152 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 20:59:16.168  INFO 15152 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 20:59:16.178  INFO 15152 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 20:59:16.239  WARN 15152 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 20:59:17.060  WARN 15152 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 1c10b0f8-19dd-4a57-83e2-db439abdedad

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 20:59:17.181  INFO 15152 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@3e140266, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5263f9fc, org.springframework.security.web.context.SecurityContextPersistenceFilter@109f6bff, org.springframework.security.web.header.HeaderWriterFilter@5d4a0bb, org.springframework.web.filter.CorsFilter@2d6da738, org.springframework.security.web.authentication.logout.LogoutFilter@7fe218ef, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@653bb3dc, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@35929e2d, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@47f5b5, org.springframework.security.web.session.SessionManagementFilter@5a968ad4, org.springframework.security.web.access.ExceptionTranslationFilter@5633aa8d, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@7201db0c]
2025-08-03 20:59:17.518  INFO 15152 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 20:59:17.555  INFO 15152 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 20:59:17.566  INFO 15152 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.086 seconds (JVM running for 7.235)
2025-08-03 21:00:11.110  INFO 15152 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 21:00:11.110  INFO 15152 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 21:00:11.114  INFO 15152 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-08-03 21:01:30.750  INFO 15152 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 21:01:30.758  INFO 15152 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 21:01:30.767  INFO 15152 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 21:01:34.404  INFO 1780 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 1780 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 21:01:34.406 DEBUG 1780 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 21:01:34.407  INFO 1780 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 21:01:34.487  INFO 1780 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 21:01:34.487  INFO 1780 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 21:01:35.295  INFO 1780 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 21:01:35.404  INFO 1780 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 100 ms. Found 12 JPA repository interfaces.
2025-08-03 21:01:35.989  INFO 1780 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 21:01:35.998  INFO 1780 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 21:01:35.998  INFO 1780 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 21:01:36.058  INFO 1780 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 21:01:36.058  INFO 1780 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1570 ms
2025-08-03 21:01:36.151  INFO 1780 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 21:01:36.549  INFO 1780 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 21:01:36.653  INFO 1780 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 21:01:36.714  INFO 1780 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 21:01:36.902  INFO 1780 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 21:01:37.001  INFO 1780 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 21:01:37.699  INFO 1780 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 21:01:37.708  INFO 1780 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 21:01:37.757  WARN 1780 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 21:01:38.515  WARN 1780 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 01f406e0-d346-4a85-a910-b1d6745cdb46

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 21:01:38.626  INFO 1780 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@43a54f6f, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@50a87a81, org.springframework.security.web.context.SecurityContextPersistenceFilter@292d356a, org.springframework.security.web.header.HeaderWriterFilter@45cf01a1, org.springframework.web.filter.CorsFilter@318d2990, org.springframework.security.web.authentication.logout.LogoutFilter@1a8e09a4, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@ecd1027, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@e93939c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@23015e80, org.springframework.security.web.session.SessionManagementFilter@d217a32, org.springframework.security.web.access.ExceptionTranslationFilter@23115451, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@a9db4a5]
2025-08-03 21:01:38.939  INFO 1780 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 21:01:38.973  INFO 1780 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 21:01:38.983  INFO 1780 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.097 seconds (JVM running for 6.054)
2025-08-03 21:01:46.108  INFO 1780 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 21:01:46.108  INFO 1780 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 21:01:46.111  INFO 1780 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-03 21:12:40.839  INFO 1780 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 21:12:40.843  INFO 1780 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 21:12:40.855  INFO 1780 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 21:12:46.570  INFO 20072 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 20072 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 21:12:46.572 DEBUG 20072 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 21:12:46.572  INFO 20072 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 21:12:46.650  INFO 20072 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 21:12:46.651  INFO 20072 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 21:12:47.423  INFO 20072 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 21:12:47.523  INFO 20072 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 93 ms. Found 12 JPA repository interfaces.
2025-08-03 21:12:48.117  INFO 20072 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 21:12:48.126  INFO 20072 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 21:12:48.127  INFO 20072 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 21:12:48.188  INFO 20072 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 21:12:48.189  INFO 20072 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1538 ms
2025-08-03 21:12:48.274  INFO 20072 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 21:12:48.734  INFO 20072 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 21:12:48.843  INFO 20072 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 21:12:48.900  INFO 20072 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 21:12:49.079  INFO 20072 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 21:12:49.177  INFO 20072 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 21:12:49.842  INFO 20072 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 21:12:49.859  INFO 20072 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 21:12:49.906  WARN 20072 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 21:12:50.657  WARN 20072 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 18b9996d-6112-4833-9fd7-e70bd2b47a2d

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 21:12:50.777  INFO 20072 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@50a87a81, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@318d2990, org.springframework.security.web.context.SecurityContextPersistenceFilter@2fdb2a97, org.springframework.security.web.header.HeaderWriterFilter@4efa4627, org.springframework.web.filter.CorsFilter@23015e80, org.springframework.security.web.authentication.logout.LogoutFilter@5008b3e9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7eabc4d0, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@59e9d73c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4ab6a3b8, org.springframework.security.web.session.SessionManagementFilter@9b51043, org.springframework.security.web.access.ExceptionTranslationFilter@367eaf9a, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2c8b0bb9]
2025-08-03 21:12:51.097  INFO 20072 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 21:12:51.134  INFO 20072 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 21:12:51.142  INFO 20072 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.093 seconds (JVM running for 6.02)
2025-08-03 21:13:04.407  INFO 20072 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 21:13:04.408  INFO 20072 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 21:13:04.412  INFO 20072 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-03 21:33:29.718  INFO 20072 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 21:33:29.722  INFO 20072 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 21:33:29.736  INFO 20072 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 21:36:57.260  INFO 19948 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 19948 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 21:36:57.261 DEBUG 19948 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 21:36:57.262  INFO 19948 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 21:36:57.347  INFO 19948 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 21:36:57.348  INFO 19948 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 21:36:58.343  INFO 19948 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 21:36:58.486  INFO 19948 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 133 ms. Found 12 JPA repository interfaces.
2025-08-03 21:36:59.215  INFO 19948 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 21:36:59.229  INFO 19948 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 21:36:59.230  INFO 19948 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 21:36:59.331  INFO 19948 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 21:36:59.331  INFO 19948 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1982 ms
2025-08-03 21:36:59.479  INFO 19948 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 21:36:59.986  INFO 19948 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 21:37:00.129  INFO 19948 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 21:37:00.199  INFO 19948 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 21:37:00.395  INFO 19948 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 21:37:00.520  INFO 19948 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 21:37:01.354  INFO 19948 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 21:37:01.365  INFO 19948 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 21:37:01.420  WARN 19948 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 21:37:02.296  WARN 19948 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 89f185bb-d0fe-4829-a3df-d15d56aa2fa4

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 21:37:02.425  INFO 19948 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@1e8900c5, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@475841c3, org.springframework.security.web.context.SecurityContextPersistenceFilter@16412364, org.springframework.security.web.header.HeaderWriterFilter@19642448, org.springframework.web.filter.CorsFilter@30a7e358, org.springframework.security.web.authentication.logout.LogoutFilter@5f865e5d, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@51d6d8c2, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@19b47c7c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4bcc7454, org.springframework.security.web.session.SessionManagementFilter@1f4c3681, org.springframework.security.web.access.ExceptionTranslationFilter@a3aeaec, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@64526d60]
2025-08-03 21:37:02.809  INFO 19948 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 21:37:02.849  INFO 19948 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 21:37:02.861  INFO 19948 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.124 seconds (JVM running for 7.303)
2025-08-03 21:38:02.166  INFO 19948 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 21:38:02.166  INFO 19948 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 21:38:02.167  INFO 19948 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-03 21:54:43.054  WARN 19948 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=11m12s835ms807µs500ns).
2025-08-03 22:04:02.615  INFO 19948 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 22:04:02.621  INFO 19948 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 22:04:02.628  INFO 19948 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 22:04:11.360  INFO 23028 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 23028 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 22:04:11.362 DEBUG 23028 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 22:04:11.363  INFO 23028 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 22:04:11.460  INFO 23028 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 22:04:11.461  INFO 23028 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 22:04:12.268  INFO 23028 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 22:04:12.365  INFO 23028 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 87 ms. Found 12 JPA repository interfaces.
2025-08-03 22:04:12.916  INFO 23028 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 22:04:12.926  INFO 23028 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 22:04:12.926  INFO 23028 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 22:04:12.994  INFO 23028 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 22:04:12.994  INFO 23028 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1533 ms
2025-08-03 22:04:13.088  INFO 23028 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 22:04:13.460  INFO 23028 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 22:04:13.578  INFO 23028 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 22:04:13.632  INFO 23028 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 22:04:13.809  INFO 23028 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 22:04:13.893  INFO 23028 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 22:04:14.565  INFO 23028 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 22:04:14.578  INFO 23028 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 22:04:14.625  WARN 23028 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 22:04:15.332  WARN 23028 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 047ea584-2110-4d1e-883e-771e74eb9a1b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 22:04:15.440  INFO 23028 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@54c4ae8b, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@28c76b39, org.springframework.security.web.context.SecurityContextPersistenceFilter@62fed1c7, org.springframework.security.web.header.HeaderWriterFilter@63921a1e, org.springframework.web.filter.CorsFilter@27e8c589, org.springframework.security.web.authentication.logout.LogoutFilter@33948ebe, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@9b51043, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@7eabc4d0, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@32e41f4e, org.springframework.security.web.session.SessionManagementFilter@2049ef2d, org.springframework.security.web.access.ExceptionTranslationFilter@b646da4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1e4471ce]
2025-08-03 22:04:15.760  INFO 23028 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 22:04:15.799  INFO 23028 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 22:04:15.814  INFO 23028 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.008 seconds (JVM running for 5.937)
2025-08-03 22:04:20.954  INFO 23028 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 22:04:20.955  INFO 23028 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 22:04:20.958  INFO 23028 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 3 ms
2025-08-03 22:39:09.860  INFO 23028 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 22:39:09.869  INFO 23028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 22:39:09.874  INFO 23028 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 22:39:22.564  INFO 9396 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 9396 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 22:39:22.566 DEBUG 9396 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 22:39:22.566  INFO 9396 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 22:39:22.669  INFO 9396 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 22:39:22.669  INFO 9396 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 22:39:23.611  INFO 9396 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 22:39:23.730  INFO 9396 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 110 ms. Found 12 JPA repository interfaces.
2025-08-03 22:39:24.423  INFO 9396 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 22:39:24.436  INFO 9396 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 22:39:24.436  INFO 9396 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 22:39:24.522  INFO 9396 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 22:39:24.522  INFO 9396 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1852 ms
2025-08-03 22:39:24.635  INFO 9396 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 22:39:25.132  INFO 9396 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 22:39:25.261  INFO 9396 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 22:39:25.317  INFO 9396 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 22:39:25.597  INFO 9396 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 22:39:25.740  INFO 9396 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 22:39:26.546  INFO 9396 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 22:39:26.559  INFO 9396 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 22:39:26.614  WARN 9396 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 22:39:27.516  WARN 9396 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 2f2c71e3-f06e-450c-8068-ba5529587c86

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 22:39:27.652  INFO 9396 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@65ce0891, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@317cc6e3, org.springframework.security.web.context.SecurityContextPersistenceFilter@10ace4fa, org.springframework.security.web.header.HeaderWriterFilter@5a968ad4, org.springframework.web.filter.CorsFilter@4bb6be7f, org.springframework.security.web.authentication.logout.LogoutFilter@ff78e2a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@7cb624b4, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@8375c76, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@452eecc4, org.springframework.security.web.session.SessionManagementFilter@1e7bbf11, org.springframework.security.web.access.ExceptionTranslationFilter@643a0c69, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@47f5b5]
2025-08-03 22:39:28.114  INFO 9396 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 22:39:28.160  INFO 9396 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 22:39:28.171  INFO 9396 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 6.217 seconds (JVM running for 7.437)
2025-08-03 22:39:29.148  INFO 9396 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 22:39:29.149  INFO 9396 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 22:39:29.151  INFO 9396 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-03 22:42:45.382  INFO 9396 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 22:42:45.389  INFO 9396 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-03 22:42:45.394  INFO 9396 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-03 22:42:57.434  INFO 20076 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Starting MedicineSystemApplication using Java 23 on LAPTOP-41EH0LH0 with PID 20076 (C:\Users\<USER>\Desktop\doctorSystem\medicine-system\target\classes started by 10354 in C:\Users\<USER>\Desktop\doctorSystem)
2025-08-03 22:42:57.436 DEBUG 20076 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Running with Spring Boot v2.7.15, Spring v5.3.29
2025-08-03 22:42:57.437  INFO 20076 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : No active profile set, falling back to 1 default profile: "default"
2025-08-03 22:42:57.527  INFO 20076 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : Devtools property defaults active! Set 'spring.devtools.add-properties' to 'false' to disable
2025-08-03 22:42:57.528  INFO 20076 --- [restartedMain] .e.DevToolsPropertyDefaultsPostProcessor : For additional web related logging consider setting the 'logging.level.web' property to 'DEBUG'
2025-08-03 22:42:58.420  INFO 20076 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-03 22:42:58.527  INFO 20076 --- [restartedMain] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 100 ms. Found 12 JPA repository interfaces.
2025-08-03 22:42:59.188  INFO 20076 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat initialized with port(s): 8080 (http)
2025-08-03 22:42:59.196  INFO 20076 --- [restartedMain] o.apache.catalina.core.StandardService   : Starting service [Tomcat]
2025-08-03 22:42:59.197  INFO 20076 --- [restartedMain] org.apache.catalina.core.StandardEngine  : Starting Servlet engine: [Apache Tomcat/9.0.79]
2025-08-03 22:42:59.265  INFO 20076 --- [restartedMain] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring embedded WebApplicationContext
2025-08-03 22:42:59.265  INFO 20076 --- [restartedMain] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1736 ms
2025-08-03 22:42:59.359  INFO 20076 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-03 22:42:59.837  INFO 20076 --- [restartedMain] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-03 22:42:59.957  INFO 20076 --- [restartedMain] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-03 22:43:00.014  INFO 20076 --- [restartedMain] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 5.6.15.Final
2025-08-03 22:43:00.253  INFO 20076 --- [restartedMain] o.hibernate.annotations.common.Version   : HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-03 22:43:00.355  INFO 20076 --- [restartedMain] org.hibernate.dialect.Dialect            : HHH000400: Using dialect: org.hibernate.dialect.MySQL8Dialect
2025-08-03 22:43:01.130  INFO 20076 --- [restartedMain] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-03 22:43:01.143  INFO 20076 --- [restartedMain] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-03 22:43:01.204  WARN 20076 --- [restartedMain] JpaBaseConfiguration$JpaWebConfiguration : spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-03 22:43:02.136  WARN 20076 --- [restartedMain] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: b50c2ac4-ce25-43cc-80e9-73b4af4f7c48

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-03 22:43:02.257  INFO 20076 --- [restartedMain] o.s.s.web.DefaultSecurityFilterChain     : Will secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@28c76b39, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@27e8c589, org.springframework.security.web.context.SecurityContextPersistenceFilter@d217a32, org.springframework.security.web.header.HeaderWriterFilter@b3011de, org.springframework.web.filter.CorsFilter@32e41f4e, org.springframework.security.web.authentication.logout.LogoutFilter@53232df9, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3d49abed, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@3e6d280a, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@43a54f6f, org.springframework.security.web.session.SessionManagementFilter@6c4d0bae, org.springframework.security.web.access.ExceptionTranslationFilter@a53e52c, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@36820641]
2025-08-03 22:43:02.628  INFO 20076 --- [restartedMain] o.s.b.d.a.OptionalLiveReloadServer       : LiveReload server is running on port 35729
2025-08-03 22:43:02.677  INFO 20076 --- [restartedMain] o.s.b.w.embedded.tomcat.TomcatWebServer  : Tomcat started on port(s): 8080 (http) with context path ''
2025-08-03 22:43:02.689  INFO 20076 --- [restartedMain] c.e.medicine.MedicineSystemApplication   : Started MedicineSystemApplication in 5.839 seconds (JVM running for 6.793)
2025-08-03 22:43:16.479  INFO 20076 --- [http-nio-8080-exec-1] o.a.c.c.C.[Tomcat].[localhost].[/]       : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-03 22:43:16.479  INFO 20076 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-03 22:43:16.483  INFO 20076 --- [http-nio-8080-exec-1] o.s.web.servlet.DispatcherServlet        : Completed initialization in 4 ms
2025-08-03 22:43:45.316  WARN 20076 --- [http-nio-8080-exec-9] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Error: 1451, SQLState: 23000
2025-08-03 22:43:45.316 ERROR 20076 --- [http-nio-8080-exec-9] o.h.engine.jdbc.spi.SqlExceptionHelper   : Cannot delete or update a parent row: a foreign key constraint fails (`medicine_db`.`purchase`, CONSTRAINT `FKru5ru4j1f33sl9vo1rtxhq5lp` FOREIGN KEY (`medicine_id`) REFERENCES `medicine` (`id`))
2025-08-03 22:43:45.317  INFO 20076 --- [http-nio-8080-exec-9] o.h.e.j.b.internal.AbstractBatchImpl     : HHH000010: On release of batch it still contained JDBC statements
