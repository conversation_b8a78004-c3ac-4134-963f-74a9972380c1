import request from '../index';
import type { Setting } from '@/types';

// 获取所有设置
export const getAllSettings = () => {
  return request.get<Setting[]>('/setting/list');
};

// 获取设置详情
export const getSettingDetail = (id: number) => {
  return request.get<Setting>(`/setting/get/${id}`);
};

// 添加设置
export const addSetting = (data: Setting) => {
  return request.post<Setting>('/setting/add', data);
};

// 更新设置
export const updateSetting = (id: number, data: Setting) => {
  return request.put<Setting>(`/setting/update/${id}`, data);
};

// 删除设置
export const deleteSetting = (id: number) => {
  return request.delete(`/setting/delete/${id}`);
};
