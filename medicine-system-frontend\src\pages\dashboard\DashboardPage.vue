<template>
  <div class="dashboard-page">
    <!-- 欢迎区域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="welcome-text">
          <h1>🏥 医药管理系统 - 仪表盘</h1>
          <p>欢迎回来，{{ getCurrentUser() }}！</p>
        </div>
        <el-button type="danger" @click="handleLogout">
          <el-icon><SwitchButton /></el-icon>
          快速退出
        </el-button>
      </div>
    </div>

    <!-- 数据概览卡片 -->
    <div class="overview-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon medicine">
                <el-icon><i class="el-icon-medicine-box" /></el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ dashboardData.medicineCount }}</div>
                <div class="overview-label">药品总数</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon inventory">
                <el-icon><Box /></el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ dashboardData.inventoryCount }}</div>
                <div class="overview-label">库存总量</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon purchase">
                <el-icon><ShoppingCart /></el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ dashboardData.purchaseCount }}</div>
                <div class="overview-label">本月采购</div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="overview-card">
            <div class="overview-content">
              <div class="overview-icon sales">
                <el-icon><Sell /></el-icon>
              </div>
              <div class="overview-info">
                <div class="overview-number">{{ dashboardData.salesCount }}</div>
                <div class="overview-label">本月销售</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 快捷操作区域 -->
    <div class="quick-actions-section">
      <el-card>
        <template #header>
          <h3>快捷操作</h3>
        </template>
        <div class="quick-actions">
          <el-button
            type="primary"
            size="large"
            class="action-button"
            @click="navigateTo('/medicine')"
          >
            <el-icon><i class="el-icon-medicine-box" /></el-icon>
            药品管理
          </el-button>

          <el-button
            type="success"
            size="large"
            class="action-button"
            @click="navigateTo('/inventory')"
          >
            <el-icon><Box /></el-icon>
            库存管理
          </el-button>

          <el-button
            type="warning"
            size="large"
            class="action-button"
            @click="navigateTo('/purchase')"
          >
            <el-icon><ShoppingCart /></el-icon>
            采购管理
          </el-button>

          <el-button
            type="info"
            size="large"
            class="action-button"
            @click="navigateTo('/sale')"
          >
            <el-icon><Sell /></el-icon>
            销售管理
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- 最近活动区域 -->
    <div class="recent-activities-section">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>最近活动</h3>
            </template>
            <div class="activity-list">
              <div
                v-for="activity in recentActivities"
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon" :class="activity.type">
                  <el-icon>
                    <component :is="activity.icon" />
                  </el-icon>
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card>
            <template #header>
              <h3>系统状态</h3>
            </template>
            <div class="system-status">
              <div class="status-item">
                <span class="status-label">系统运行时间</span>
                <span class="status-value">{{ systemStatus.uptime }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">在线用户</span>
                <span class="status-value">{{ systemStatus.onlineUsers }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">数据库状态</span>
                <el-tag :type="systemStatus.dbStatus === '正常' ? 'success' : 'danger'">
                  {{ systemStatus.dbStatus }}
                </el-tag>
              </div>
              <div class="status-item">
                <span class="status-label">服务器状态</span>
                <el-tag :type="systemStatus.serverStatus === '正常' ? 'success' : 'danger'">
                  {{ systemStatus.serverStatus }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  Box,
  ShoppingCart,
  Sell,
  SwitchButton
} from '@element-plus/icons-vue';
import { getDashboardStats, type DashboardStats } from '@/api/modules/report';

const router = useRouter();

// 仪表盘数据
const dashboardData = reactive({
  medicineCount: 0,
  inventoryCount: 0,
  purchaseCount: 0,
  salesCount: 0
});

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    type: 'medicine',
    icon: 'Medicine',
    title: '新增药品：阿莫西林胶囊',
    time: '2小时前'
  },
  {
    id: 2,
    type: 'inventory',
    icon: 'Box',
    title: '库存入库：感冒灵颗粒 100盒',
    time: '4小时前'
  },
  {
    id: 3,
    type: 'purchase',
    icon: 'ShoppingCart',
    title: '采购订单：PO202501030001 已完成',
    time: '6小时前'
  },
  {
    id: 4,
    type: 'sales',
    icon: 'Sell',
    title: '销售订单：SO202501030001 已发货',
    time: '8小时前'
  }
]);

// 系统状态
const systemStatus = reactive({
  uptime: '7天 12小时 30分钟',
  onlineUsers: 5,
  dbStatus: '正常',
  serverStatus: '正常'
});

// 获取当前用户名
const getCurrentUser = () => {
  const userInfo = localStorage.getItem('userInfo');
  if (userInfo) {
    try {
      const user = JSON.parse(userInfo);
      return user.username || 'hhh';
    } catch {
      return 'hhh';
    }
  }
  return 'hhh';
};

// 导航到指定页面
const navigateTo = (path: string) => {
  router.push(path);
};

// 获取仪表盘数据
const fetchDashboardData = async () => {
  try {
    console.log('开始获取仪表盘数据...');
    const response = await getDashboardStats();
    console.log('仪表盘数据响应:', response);

    if (response) {
      dashboardData.medicineCount = response.medicineCount || 0;
      dashboardData.inventoryCount = response.inventoryCount || 0;
      dashboardData.purchaseCount = response.purchaseCount || 0;
      dashboardData.salesCount = response.salesCount || 0;

      console.log('仪表盘数据更新完成:', dashboardData);
      ElMessage.success('仪表盘数据已更新');
    } else {
      console.error('未收到仪表盘数据');
      ElMessage.warning('未能获取到仪表盘数据');
    }
  } catch (error: any) {
    console.error('获取仪表盘数据失败:', error);
    ElMessage.error(error.msg || '获取仪表盘数据失败');

    // 如果API调用失败，使用默认值
    dashboardData.medicineCount = 0;
    dashboardData.inventoryCount = 0;
    dashboardData.purchaseCount = 0;
    dashboardData.salesCount = 0;
  }
};

// 退出登录
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    );

    // 清除本地存储
    localStorage.removeItem('token');
    localStorage.removeItem('userInfo');

    // 跳转到登录页
    router.push('/login');
    ElMessage.success('已退出登录');
  } catch {
    // 用户取消退出
  }
};

// 页面加载时获取数据
onMounted(() => {
  fetchDashboardData();
});
</script>

<style scoped>
.dashboard-page {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 欢迎区域 */
.welcome-section {
  margin-bottom: 20px;
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.welcome-text h1 {
  margin: 0 0 10px 0;
  font-size: 28px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  font-size: 16px;
  opacity: 0.9;
}

/* 数据概览区域 */
.overview-section {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.overview-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 20px;
}

.overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  margin-right: 20px;
}

.overview-icon.medicine {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-icon.inventory {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-icon.purchase {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-icon.sales {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.overview-info {
  flex: 1;
}

.overview-number {
  font-size: 32px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
}

.overview-label {
  font-size: 14px;
  color: #7f8c8d;
  font-weight: 500;
}

/* 快捷操作区域 */
.quick-actions-section {
  margin-bottom: 20px;
}

.quick-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.action-button {
  min-width: 160px;
  height: 60px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 最近活动区域 */
.recent-activities-section {
  margin-bottom: 20px;
}

.activity-list {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 15px 0;
  border-bottom: 1px solid #ebeef5;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  margin-right: 15px;
}

.activity-icon.medicine {
  background: #409eff;
}

.activity-icon.inventory {
  background: #67c23a;
}

.activity-icon.purchase {
  background: #e6a23c;
}

.activity-icon.sales {
  background: #f56c6c;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
}

.activity-time {
  font-size: 12px;
  color: #909399;
}

/* 系统状态区域 */
.system-status {
  padding: 10px 0;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.status-value {
  font-size: 14px;
  color: #303133;
  font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }

  .quick-actions {
    flex-direction: column;
    align-items: center;
  }

  .action-button {
    width: 100%;
    max-width: 300px;
  }
}
</style>
