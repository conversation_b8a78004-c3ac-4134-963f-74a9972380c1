import request from '../index';

// 报表概览数据类型
export interface ReportOverview {
  totalSales: number;
  salesGrowth: number;
  totalPurchase: number;
  purchaseGrowth: number;
  totalProfit: number;
  profitGrowth: number;
  totalOrders: number;
  ordersGrowth: number;
}

// 销售趋势数据类型
export interface SalesTrendData {
  xAxisData: string[];
  salesData: number[];
  purchaseData: number[];
}

// 图表数据项类型
export interface ChartDataItem {
  name: string;
  value: number;
  itemStyle?: {
    color: string;
  };
}

// 药品排行数据类型
export interface MedicineRankItem {
  name: string;
  sales: number;
}

// 热销药品数据类型
export interface HotMedicineItem {
  rank: number;
  name: string;
  sales: number;
  amount: number;
}

// 库存预警数据类型
export interface LowStockItem {
  name: string;
  currentStock: number;
  minStock: number;
  status: string;
}

// 获取报表概览数据
export const getReportOverview = (params?: { startDate?: string; endDate?: string }) => {
  return request.get<ReportOverview>('/report/overview', { params });
};

// 获取销售趋势数据
export const getSalesTrend = (params: { 
  type?: string; 
  startDate?: string; 
  endDate?: string; 
}) => {
  return request.get<SalesTrendData>('/report/sales-trend', { params });
};

// 获取药品销售排行
export const getMedicineRank = (params?: { 
  startDate?: string; 
  endDate?: string; 
  limit?: number; 
}) => {
  return request.get<MedicineRankItem[]>('/report/medicine-rank', { params });
};

// 获取客户分布数据
export const getCustomerDistribution = () => {
  return request.get<ChartDataItem[]>('/report/customer-distribution');
};

// 获取供应商占比数据
export const getSupplierRatio = () => {
  return request.get<ChartDataItem[]>('/report/supplier-ratio');
};

// 获取库存状态数据
export const getInventoryStatus = () => {
  return request.get<ChartDataItem[]>('/report/inventory-status');
};

// 获取热销药品排行
export const getHotMedicines = (params?: { 
  startDate?: string; 
  endDate?: string; 
  limit?: number; 
}) => {
  return request.get<HotMedicineItem[]>('/report/hot-medicines', { params });
};

// 获取库存预警数据
export const getLowStock = () => {
  return request.get<LowStockItem[]>('/report/low-stock');
};

// 仪表盘统计数据类型
export interface DashboardStats {
  medicineCount: number;
  inventoryCount: number;
  purchaseCount: number;
  salesCount: number;
}

// 获取仪表盘统计数据
export const getDashboardStats = () => {
  return request.get<DashboardStats>('/report/dashboard-stats');
};
